use once_cell::sync::Lazy;
use regex::Regex;

pub static INIT_REG: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"^(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})\s+(\S+)").unwrap()
});

pub static EVENT_REG: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"\s+(\([^)]+\))").unwrap()
});

pub static ALERT_REG: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r"\s+(\[[^\]]+\])").unwrap()
});

pub static DAEMON_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static FILTERLOG_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static SNORT_PATTERN: Lazy<Regex> = Lazy::new(|| {
    let pattern = [
        INIT_REG.as_str(),
        EVENT_REG.as_str(),
        ALERT_REG.as_str(),
        r"\s+\[(\d+:\d+:\d+)\]",
        r"\s+(.+?)",
        r"\s+\[Classification: (.+?)\]",
        r"\s+\[Priority: (\d+)\]",
        r"\s+\{(\w+)\}",
        r"\s+([\d\.]+)(?::(\d+))?",
        r"\s+->",
        r"\s+([\d\.]+)(?::(\d+))?",
    ].join("");

    Regex::new(&pattern).unwrap()
});

pub static SQUID_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(
        r"{}{}{}\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        /(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        /(.+?)\
        \s+(.+)",
        INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
    )).unwrap()
});

pub static USERAUDIT_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static USERNOTICE_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(
        r"{}{}{}\
        \s+openvpn\s+server\s+'(.+?)'\
        \s+user\s+'(.+?)'\
        \s+address\s+'([\d\.]+):(\d+)'\
        \s+.+?\
        \s+(.+)",
        INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
    )).unwrap()
});

pub static USERWARNING_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static ROUTER_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static ROUTERBOARD_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static SWITCH_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static VMWARE_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static WINDOWSSERVER_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(r"{}{}{}\s+(.*)", INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str())).unwrap()
});

pub static WS_AN_AD_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r".+Account Name:\s+(.+?)\s+Account Domain:\s+(.+?)\s+Logon ID:.+").unwrap()
});

pub static WS_SW_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(r".+Source Workstation:\s+([^\s]+?)\s+Error Code:.+").unwrap()
});

pub static VPNSERVER_PATTERN: Lazy<Regex> = Lazy::new(|| {
    let pattern = [
        INIT_REG.as_str(),
        EVENT_REG.as_str(),
        ALERT_REG.as_str(),
        r".*?The user\s+([^\\]+)",
        r"\\(.+)\s+",
        r"connected on port\s+(\S+)\s+.*?",
        r"The user was active for\s+([\w\s]+?)\.",
        r"\s+(\d+)\s+bytes were sent and",
        r"\s+(\d+)\s+bytes were received",
    ].join("");

    Regex::new(&pattern).unwrap()
});

pub static DHCP_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(
        r"{}{}\s+\[MSWinEventLog.+?\]\s+.+\s+{}\
        \s+(.*)\tN/A",
        INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
    )).unwrap()
});

pub static DNS_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(&format!(
        r"{}{}\s+\[MSWinEventLog.+?\]\s+.+\s+{}\
        \s+(.*)\tN/A",
        INIT_REG.as_str(), EVENT_REG.as_str(), ALERT_REG.as_str()
    )).unwrap()
});

pub static DNS_REST_PATTERN: Lazy<Regex> = Lazy::new(|| {
    Regex::new(
        r"\s*(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(.+?)\
        \s+(R?)\
        \s+([Q\?]?)\
        \s+\[([^\s]+)\
        \s+([A-Za-z ]*?)\
        \s+([^\s]+)\]\
        \s+(.+?)\
        \s+(.+)"
    ).unwrap()
});
