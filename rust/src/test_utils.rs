use std::collections::HashMap;
use once_cell::sync::La<PERSON>;

// __Daemon__
// __Snort__
// __VPNServer__

pub static sensor_list_of_names_and_addresses: Lazy<Vec<String>> = Lazy::new(|| {
    vec![
        "***********".to_string(),
        "***********".to_string(),
        "Sensor-1".to_string(),
        "Sensor-2".to_string(),
    ]
});

pub static sensor_dict_of_addresses_and_names: Lazy<HashMap<String, String>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("***********".to_string(), "Sensor-1".to_string());
    map.insert("***********".to_string(), "Sensor-2".to_string());
    map
});

#[cfg(test)]
mod tests {
    use super::*;
    use crate::utils::*;
    use crate::utils_classes::*;
    use crate::utils_parsers::*;
    use std::collections::HashMap;

    #[test]
    fn test_normalize_date() {
        // MM/DD/YYYY format
        assert_eq!(normalize_date("12/8/2020"),  "2020-12-08");
        assert_eq!(normalize_date("12/31/2020"), "2020-12-31");
        assert_eq!(normalize_date("01/01/2000"), "2000-01-01");
        assert_eq!(normalize_date("12/31/1999"), "1999-12-31");

        // MM/DD/YY format
        assert_eq!(normalize_date("12/8/20"),  "2020-12-08");
        assert_eq!(normalize_date("12/31/20"), "2020-12-31");
        assert_eq!(normalize_date("01/01/00"), "2000-01-01");
        assert_eq!(normalize_date("12/31/99"), "1999-12-31");

        // invalid format
        assert_eq!(normalize_date("31/12/2020"),   "31/12/2020");
        assert_eq!(normalize_date("2020-12-31"),   "2020-12-31");
        assert_eq!(normalize_date("invalid date"), "invalid date");
        assert_eq!(normalize_date(""),             "");
    }

    #[test]
    fn test_normalize_dns_question_name() {
        assert_eq!(normalize_dns_question_name("example(123)com"),              "example.com");
        assert_eq!(normalize_dns_question_name("(456)example(789)com"),         "example.com");
        assert_eq!(normalize_dns_question_name("example(123)com(456)"),         "example.com");
        assert_eq!(normalize_dns_question_name("sub(1)domain(2)example(3)com"), "sub.domain.example.com");
        assert_eq!(normalize_dns_question_name("example.com"),                  "example.com");
        assert_eq!(normalize_dns_question_name(""),                             "");
        assert_eq!(normalize_dns_question_name("(123)(456)(789)"),              "");
    }

    #[test]
    fn test_normalize_time() {
        assert_eq!(normalize_time("12:00:00 AM"), "00:00:00");
        assert_eq!(normalize_time("01:30:45 AM"), "01:30:45");
        assert_eq!(normalize_time("11:59:59 AM"), "11:59:59");
        assert_eq!(normalize_time("12:00:00 PM"), "12:00:00");
        assert_eq!(normalize_time("01:30:45 PM"), "13:30:45");
        assert_eq!(normalize_time("11:59:59 PM"), "23:59:59");
        assert_eq!(normalize_time("1:15:30 AM"),  "01:15:30");
        assert_eq!(normalize_time("9:45:00 PM"),  "21:45:00");
        assert_eq!(normalize_time("00:00:00 AM"), "00:00:00");
        assert_eq!(normalize_time("13:30:45 PM"), "13:30:45");

        // invalid format
        assert_eq!(normalize_time("25:00:00 PM"), "25:00:00 PM");
        assert_eq!(normalize_time("12:60:00 AM"), "12:60:00 AM");
        assert_eq!(normalize_time("not a time"),  "not a time");
        assert_eq!(normalize_time("12:00:00"),    "12:00:00");
    }

    #[test]
    fn test_is_invalid_ln() {
        assert_eq!(_is_invalid_ln(""), true);
        assert_eq!(_is_invalid_ln("2025-06-21 07:10:12 MSYADDSI (user/notice) [MSWinEventLog       1       N/A     98031276        Sat] Jun 21 07:10:12 2025       N/A     N/A     N/A   N/A      N/A     N/A     N/A             [dns] 6/21/2025 7:10:12 AM 0C20 PACKET  00000294AED611C0 UDP Rcv 192.168.11.29   08a1   Q [0001   D   NOERROR]        (1)h(1)l(1)l(1)l(1-- [ERROR name exceeds safe print buffer length]    N/A"), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (user/notice) [MSWinEventLog    1       N/A     136220     Thu] Jun 12 23:58:47 2025       N/A     N/A     N/A     N/A     N/AN/A     N/A             [dns] 1/12/2025 11:58:47 PM 0FAC PACKET  00000124085DBD40 UDP Rcv *******    4567   Q [0001   D   NOERROR]        (4)ted[ERROR length byte: 0x63]        N/A"), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (user/notice) [MSWinEventLog    1       N/A     135124     Thu] Jun 12 23:22:18 2025       N/A     N/A     N/A     N/A     N/AN/A     N/A             [dns] 1/12/2025 11:22:18 PM 0FA4 PACKET  000001247B3794E0 UDP Rcv *******    4567   Q [0001   D   NOERROR]        (2)1x(49)[ERROR length byte: 0x31 at 000001247B37AE30 leads outside message]   N/A"), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (auth/info) [sshguard] Exiting on signal."), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (auth/info) [sshguard] Now monitoring attacks."), true);
        assert_eq!(_is_invalid_ln("2023-05-12 23:36:10 Object-1 (auth/alert) [snort] (spp_arpspoof) Unicast ARP request"), true);
        assert_eq!(_is_invalid_ln("FATAL: Cannot open '/var/squid/logs' because it is a directory, not a file."), true);
        assert_eq!(_is_invalid_ln("MIICGDCCAQACAQEwDQYJKoZIhvcNAQELBQAwgZsxCzAJBgNVBAMMAkNBMRcwFQYK"), true);
    }

    // __Daemon__

    #[test]
    fn TestParseLnDaemon__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = DaemonConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 Sensor-1 {} [charon] 05[IKE] <con2|204> activating new tasks",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Daemon,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[charon]");
            assert_eq!(parsed[4], "05[IKE] <con2|204> activating new tasks");
        }
    }

    #[test]
    fn TestParseLnDaemon__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = DaemonConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [charon] 05[IKE] <con2|204> activating new tasks",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Daemon,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0], "2023-05-12");
            assert_eq!(parsed[1], "23:36:10");
            assert_eq!(parsed[2], e_t.to_string());
            assert_eq!(parsed[3], "[charon]");
            assert_eq!(parsed[4], "05[IKE] <con2|204> activating new tasks");
        }
    }

    #[test]
    fn TestParseLnDaemon__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = DaemonConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Sensor {} [charon] 05[IKE] <con2|204> activating new tasks",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Daemon,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnDaemon__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [charon] 05[IKE] <con2|204> activating new tasks";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Daemon,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnDaemon__test_invalid_alert_type() {
        // no-op
    }

    #[test]
    fn TestParseLnDaemon__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Daemon,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __Snort__

    #[test]
    fn TestParseLnSnort__test_valid_line() {
        let event_types = if let MYSQLValue::List(types) = SnortConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 Sensor-1 {} [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {{TCP}} *******:94 -> *******:32",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Snort,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0],  "2023-05-12");
            assert_eq!(parsed[1],  "23:36:10");
            assert_eq!(parsed[2],  "1:1448:20");
            assert_eq!(parsed[3],  "POLICY-OTHER Microsoft");
            assert_eq!(parsed[4],  "Generic Command");
            assert_eq!(parsed[5],  "3");
            assert_eq!(parsed[6],  "TCP");
            assert_eq!(parsed[7],  "*******");
            assert_eq!(parsed[8],  "94");
            assert_eq!(parsed[9],  "*******");
            assert_eq!(parsed[10], "32");
        }
    }

    #[test]
    fn TestParseLnSnort__test_mapping_object_ip_to_object_name() {
        let event_types = if let MYSQLValue::List(types) = SnortConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        for e_t in &event_types {
            let ln = format!(
                "2023-05-12 23:36:10 *********** {} [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {{TCP}} *******:94 -> *******:32",
                e_t
            );

            let (object_name, parsed_ln) = parse_ln(
                &ln,
                ConfigType::Snort,
                &sensor_list_of_names_and_addresses,
                &sensor_dict_of_addresses_and_names,
            );

            assert_eq!(object_name, Some("Sensor-1".to_string()));

            let Some(parsed) = parsed_ln else {
                panic!("Expected parsed_ln to be Some, but got None");
            };
            assert_eq!(parsed[0],  "2023-05-12");
            assert_eq!(parsed[1],  "23:36:10");
            assert_eq!(parsed[2],  "1:1448:20");
            assert_eq!(parsed[3],  "POLICY-OTHER Microsoft");
            assert_eq!(parsed[4],  "Generic Command");
            assert_eq!(parsed[5],  "3");
            assert_eq!(parsed[6],  "TCP");
            assert_eq!(parsed[7],  "*******");
            assert_eq!(parsed[8],  "94");
            assert_eq!(parsed[9],  "*******");
            assert_eq!(parsed[10], "32");
        }
    }

    #[test]
    fn TestParseLnSnort__test_invalid_object_name() {
        let event_types = if let MYSQLValue::List(types) = SnortConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Invalid-Sensor {} [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {{TCP}} *******:94 -> *******:32",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Snort,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSnort__test_invalid_event_type() {
        let ln = "2023-05-12 23:36:10 Sensor-1 (invalid-event-type) [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} *******:94 -> *******:32";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Snort,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSnort__test_invalid_alert_type() {
        let event_types = if let MYSQLValue::List(types) = SnortConfig::EVENT_TYPES.value() {
            types
        } else {
            vec![]
        };

        let event_type = &event_types[0].clone();
        let ln = format!(
            "2023-05-12 23:36:10 Sensor-1 {} [invalid-alert-type] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {{TCP}} *******:94 -> *******:32",
            event_type
        );

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Snort,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    #[test]
    fn TestParseLnSnort__test_invalid_line() {
        let ln = "2023-05-12 23:36:10 This is an invalid line";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::Snort,
            &sensor_list_of_names_and_addresses,
            &sensor_dict_of_addresses_and_names,
        );

        assert_eq!(object_name, None);
        assert_eq!(parsed_ln, None);
    }

    // __VPNServer__

    #[test]
    fn TestParseLnVPNServer__test_valid_line() {
        let ln = "2023-05-12 23:36:10 VPNSERVER (user/info) [MSWinEventLog	1	System	5061875	Mon] Jun 30 20:28:56 2025       20272   RemoteAccess    N/A     N/A     Information     VPNSERVER.sth.local     N/A         RoutingDomainID- {********-0000-0000-0000-********0000}: CoID={NA}: The user MYDOMAIN\n.peterson connected on port VPN1-123 on 6/30/2025 at 5:36 PM and disconnected on 6/30/2025 at 8:28 PM.  The user was active for 172 minutes 12 seconds.  ******** bytes were sent and 7613881 bytes were received. The reason for disconnecting was user request. The tunnel used was WAN Miniport (PPTP). The quarantine state was .      654321";

        let (object_name, parsed_ln) = parse_ln(
            &ln,
            ConfigType::VPNServer,
            &[],
            &std::collections::HashMap::new(),
        );

        assert_eq!(object_name, None);

        let Some(parsed) = parsed_ln else {
            panic!("Expected parsed_ln to be Some, but got None");
        };
        assert_eq!(parsed[0], "2023-05-12");
        assert_eq!(parsed[1], "23:36:10");
        assert_eq!(parsed[2], "MYDOMAIN");
        assert_eq!(parsed[3], "n.peterson");
        assert_eq!(parsed[4], "VPN1-123");
        assert_eq!(parsed[5], "172 minutes 12 seconds");
        assert_eq!(parsed[6], "********");
        assert_eq!(parsed[7], "7613881");
    }

}
